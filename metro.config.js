const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add resolver configuration to handle react-native-svg issues
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Add alias to resolve the util path issue
config.resolver.alias = {
  ...config.resolver.alias,
  '../../lib/util': require.resolve('react-native-svg/lib/util'),
};

module.exports = config;
