# Document Scanner Enhancement Setup

## Overview
The UploadModal component has been enhanced with the `react-native-document-scanner-plugin` library to provide professional document scanning capabilities.

## Installation Required

To complete the setup, you need to install the document scanner package:

```bash
npm install react-native-document-scanner-plugin
```

## New Features Added

### 1. Document Scanner Button
- Added a new "Scan Document" button alongside the existing "Take Photo" and "Select File" options
- Uses a distinctive green color (#00D4AA) to match the app's theme
- Features a ScanLine icon for clear visual identification

### 2. Enhanced Processing Flow
- **Scanned documents**: Skip the simulation animation and go directly to preview since they're already enhanced
- **Regular photos/files**: Continue with the existing scanning animation and processing simulation
- Different processing messages based on document type

### 3. Smart Document Handling
- Scanned documents are marked with a `scanned: true` flag
- Different preview descriptions for scanned vs. processed documents
- Optimized user experience based on document source

## How It Works

1. **User selects "Scan Document"**
   - Opens the native document scanner interface
   - Allows user to capture and crop the document
   - Returns high-quality scanned image

2. **Processing**
   - Scanned documents bypass the simulation animation
   - Regular photos go through the existing enhancement simulation
   - All documents are saved to storage with appropriate metadata

3. **Preview**
   - Shows different descriptions based on document type
   - Maintains the original/enhanced toggle functionality
   - Provides appropriate feedback messages

## Configuration Options

The scanner is configured with:
- `maxNumDocuments: 1` - Single document scanning
- `croppedImageQuality: 100` - Maximum quality output

## Benefits

1. **Professional Quality**: The scanner provides automatic edge detection, perspective correction, and image enhancement
2. **User-Friendly**: Intuitive interface with guided document capture
3. **Consistent Experience**: Seamlessly integrates with existing upload flow
4. **Enhanced Quality**: Superior results compared to regular camera photos

## Testing

To test the new functionality:
1. Install the package using the command above
2. Run the app and navigate to the upload modal
3. Select a beneficiary
4. Try the new "Scan Document" button
5. Capture a document and verify the enhanced processing flow

## Notes

- The scanner requires camera permissions (already handled by existing camera functionality)
- Works on both iOS and Android
- Provides automatic document detection and cropping
- Maintains backward compatibility with existing photo and file upload methods
